/* 全局样式 */
:root {
    --primary-color: #7b68ee;
    --primary-hover: #6a56e0;
    --secondary-color: #9370db;
    --accent-color: #ff85a2;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #333;
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: #f5f7fa;
}

/* 侧边栏样式 */
#sidebar {
    min-height: 100vh;
    background: linear-gradient(180deg, #343a40 0%, #212529 100%);
    box-shadow: var(--box-shadow);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.8rem 1rem;
    border-radius: var(--border-radius);
    margin: 0.2rem 0;
    transition: all 0.3s;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    color: white;
    background-color: var(--primary-color);
}

/* 卡片样式 */
.card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-title {
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-primary {
    background: linear-gradient(145deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(145deg, var(--primary-hover), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(123, 104, 238, 0.3);
}

.btn-success {
    background-color: #4dd0e1;
    border: none;
}

.btn-success:hover {
    background-color: #26c6da;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(38, 198, 218, 0.3);
}

.btn-danger {
    background-color: #ff6b6b;
    border: none;
}

.btn-danger:hover {
    background-color: #ff5252;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 82, 82, 0.3);
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #e0e0e0;
    padding: 0.6rem 1rem;
    transition: all 0.3s;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(123, 104, 238, 0.25);
}

/* 表格样式 */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background-color: var(--light-color);
    font-weight: 600;
    border-bottom: 2px solid #e0e0e0;
}

.table-hover tbody tr:hover {
    background-color: rgba(123, 104, 238, 0.05);
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-link {
    color: var(--primary-color);
    border-radius: 5px;
    margin: 0 2px;
}

.page-link:hover {
    color: var(--primary-hover);
    background-color: #f0f0f0;
}

/* 模态框样式 */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
}

.modal-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #e0e0e0;
}

.modal-title {
    font-weight: 600;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.4rem 0.6rem;
    border-radius: 50px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    #sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        z-index: 1000;
        transition: left 0.3s;
    }
    
    #sidebar.show {
        left: 0;
    }
    
    main {
        margin-left: 0 !important;
    }
}

/* 少女风格元素 */
.card, .btn, .form-control, .table {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* 渐变和动画 */
.bg-gradient-primary {
    background: linear-gradient(45deg, #7b68ee, #9370db);
}

.bg-gradient-accent {
    background: linear-gradient(45deg, #ff85a2, #ff9a9e);
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c7c7c7;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
} 