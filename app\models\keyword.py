from datetime import datetime
from app import db

class Keyword(db.Model):
    __tablename__ = 'keywords'
    
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.String(255), nullable=False, index=True, comment='关键词内容')
    category = db.Column(db.Enum(
        'content', 'nickname', 'collection', 'competitor_block',
        'competitor_auction', 'competitor_asset', 'best_block','auction_block','assets_block','taobao_block',
        'taobao_baohan','collect_gq_kw', 'collect_gq_tel', 'collect_gq_userid'
    ), nullable=False, default='content', comment='关键词分类')
    status = db.Column(db.<PERSON>, default=True, comment='关键词状态：启用/禁用')
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    def __init__(self, content, category='content', status=True):
        self.content = content.strip()  # 关键词
        self.category = category
        self.status = status
    
    def to_dict(self):
        return {
            'id': self.id,
            'content': self.content,
            'category': self.category,
            'status': self.status,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def __repr__(self):
        return f'<Keyword {self.content}>'
    
    @staticmethod
    def category_text(category):
        """返回分类的中文说明"""
        category_map = {
            'content': '内容关键词',
            'nickname': '昵称关键词',
            'collection': '采集关键词',
            'competitor_block': '竞品屏蔽关键词',
            'competitor_auction': '竞品屏蔽拍卖关键词',
            'competitor_asset': '竞品屏蔽资产关键词',
            'best_block': '你最棒屏蔽关键词',
            'auction_block': '拍卖采集屏蔽关键词',
            'assets_block': '资产采集屏蔽关键词',
            'taobao_block': '淘宝采集屏蔽关键词',
            'taobao_baohan': '淘宝采集包含关键词',
            'collect_gq_kw': '供求采集屏蔽关键词',
            'collect_gq_tel': '供求采集屏蔽手机号',
            'collect_gq_userid': '供求采集屏蔽用户ID',
        }
        return category_map.get(category, '未知分类') 
