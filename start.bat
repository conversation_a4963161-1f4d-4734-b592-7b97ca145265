@echo off
chcp 65001
echo ===== 关键词管理系统启动工具 =====

REM 检查配置文件是否存在
if not exist .env (
    echo [警告] 未找到配置文件，将运行数据库配置助手...
    call db_setup.bat
    echo [信息] 配置完成，继续启动过程...
)

REM 检查是否存在虚拟环境
if not exist venv (
    echo [信息] 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo [信息] 激活虚拟环境...
call venv\Scripts\activate

REM 安装依赖
echo [信息] 安装依赖...
pip install -r requirements.txt

REM 检查数据库迁移是否已初始化
if not exist migrations (
    echo [警告] 数据库表尚未初始化！
    echo [信息] 请先运行 init_database.bat 进行数据库表初始化，然后再启动系统。
    pause
    exit /b 1
)

REM 启动应用
echo [信息] 启动关键词管理系统...
python app.py

pause 