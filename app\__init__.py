from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_caching import Cache
from config import config

# 创建扩展实例
db = SQLAlchemy()
migrate = Migrate()
cache = Cache()

def create_app(config_name='default'):
    # 创建Flask应用实例
    app = Flask(__name__)
    
    # 从配置对象加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    cache.init_app(app)
    
    # 注册蓝图
    from .controllers.keyword import keyword_bp
    app.register_blueprint(keyword_bp)
    
    # 注册错误处理器
    from .controllers.errors import errors_bp
    app.register_blueprint(errors_bp)
    
    return app 