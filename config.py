import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_COMMIT_ON_TEARDOWN = True

    # 数据库连接池配置 - 解决MySQL连接超时问题
    SQLALCHEMY_POOL_SIZE = 10  # 减少连接池大小
    SQLALCHEMY_MAX_OVERFLOW = 20
    SQLALCHEMY_POOL_RECYCLE = 3600  # 1小时回收连接
    SQLALCHEMY_POOL_PRE_PING = True  # 启用连接预检查
    SQLALCHEMY_POOL_TIMEOUT = 30  # 连接超时时间

    # 数据库引擎选项
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,  # 在使用连接前检查连接是否有效
        'pool_recycle': 3600,   # 1小时后回收连接
        'pool_timeout': 30,     # 获取连接的超时时间
        'max_overflow': 20,     # 最大溢出连接数
        'echo': False,          # 不打印SQL语句（生产环境）
        'connect_args': {
            'connect_timeout': 60,      # MySQL连接超时
            'read_timeout': 30,         # 读取超时
            'write_timeout': 30,        # 写入超时
            'charset': 'utf8mb4',       # 字符集
            'autocommit': False,        # 不自动提交
        }
    }

    CACHE_TYPE = 'SimpleCache'
    CACHE_DEFAULT_TIMEOUT = 300
    ITEMS_PER_PAGE = [50, 100, 200, 500, 1000] # 展示页条数
    DEFAULT_PER_PAGE = 50 # 默认条数

    @staticmethod
    def init_app(app):
        pass

class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'mysql+pymysql://keyword_db:SfTaBtjTSm8mLXwj@127.0.0.1:3306/keyword_db?charset=utf8mb4'

    # 开发环境特定配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        **Config.SQLALCHEMY_ENGINE_OPTIONS,
        'echo': True,  # 开发环境打印SQL语句
    }

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or \
        'mysql+pymysql://keyword_db:SfTaBtjTSm8mLXwj@127.0.0.1:3306/keyword_db_test?charset=utf8mb4'

class ProductionConfig(Config):
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'mysql+pymysql://keyword_db:SfTaBtjTSm8mLXwj@127.0.0.1:3306/keyword_db?charset=utf8mb4'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 生产环境特定配置
        import logging
        from logging.handlers import RotatingFileHandler
        
        # 创建日志文件处理器
        file_handler = RotatingFileHandler('logs/keyword_app.log', 
                                         maxBytes=10 * 1024 * 1024,
                                         backupCount=10)
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s '
            '[in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('关键词管理系统启动')

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
} 
