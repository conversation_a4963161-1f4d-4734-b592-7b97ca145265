{% extends "base.html" %}

{% block title %}500 - 服务器错误{% endblock %}

{% block header %}500 - 服务器错误{% endblock %}

{% block content %}
<div class="row mt-5">
    <div class="col-md-6 offset-md-3 text-center">
        <div class="error-container">
            <div class="error-code">500</div>
            <div class="error-message">哎呀！服务器遇到了错误</div>
            <p class="mt-3">
                我们的技术团队已经收到这个问题的通知，并正在努力解决它。<br>
                请稍后再试或返回首页。
            </p>
            <a href="{{ url_for('keyword.index') }}" class="btn btn-primary mt-3">
                <i class="fas fa-home me-1"></i> 返回首页
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .error-container {
        padding: 40px;
        border-radius: 15px;
        background: linear-gradient(145deg, #f8f9fa, #e9ecef);
        box-shadow: 5px 5px 10px #d1d9e6, -5px -5px 10px #ffffff;
    }
    .error-code {
        font-size: 120px;
        font-weight: bold;
        color: #ff6b6b;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    .error-message {
        font-size: 24px;
        font-weight: 500;
        color: #333;
        margin-bottom: 20px;
    }
</style>
{% endblock %} 