# 关键词管理系统

一个基于Flask的现代化关键词管理系统，支持多类型关键词管理，具有高性能和优美的界面。

## 功能特点

- 支持多种关键词分类：内容关键词、昵称关键词、采集关键词、竞品屏蔽关键词
- 关键词状态管理：启用/禁用
- 关键词搜索和筛选
- 批量操作功能：批量删除、批量状态更新
- 分页展示，支持每页20、50、100、200、500、1000条数据
- 高性能设计，支持数百人同时使用
- 现代科技感少女风格UI

## 技术栈

- **后端**: Python Flask, SQLAlchemy
- **数据库**: MySQL
- **前端**: HTML, CSS, JavaScript, Bootstrap 5, jQuery, Axios
- **性能优化**: Flask-Caching, 数据库索引优化

## 安装和部署

### 系统要求

- Python 3.8+
- MySQL 5.7+
- pip

### 安装步骤

1. 克隆代码库
```
git clone https://github.com/yourusername/keyword-management.git
cd keyword-management
```

2. 创建并激活虚拟环境
```
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/macOS
python -m venv venv
source venv/bin/activate
```

3. 安装依赖
```
pip install -r requirements.txt
```

4. 配置环境变量
```
# 创建.env文件，复制.env.example中的内容并修改为你的配置
cp .env.example .env
```

5. 创建数据库
```
# 在MySQL中创建数据库
mysql -u root -p
CREATE DATABASE keyword_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

6. 初始化数据库
```
flask db init
flask db migrate -m "initial migration"
flask db upgrade
```

7. 创建测试数据（可选）
```
flask create_test_data
```

8. 启动应用
```
flask run
# 或
python app.py
```

9. 访问应用
```
http://localhost:5000
```

## 开发配置

### 使用开发模式
```
# .env文件
FLASK_ENV=development
```

### 连接到不同的数据库
修改`.env`文件中的`DEV_DATABASE_URL`或`DATABASE_URL`以连接到不同的数据库。

## 生产部署

1. 设置生产环境配置
```
# .env文件
FLASK_ENV=production
SECRET_KEY=生成一个安全的密钥
```

2. 使用生产服务器
```
# 使用gunicorn启动
gunicorn -w 4 -b 0.0.0.0:5000 "app:app"
```

## 性能优化建议

- 使用连接池管理数据库连接
- 适当调整数据库缓存设置
- 使用反向代理如Nginx
- 考虑使用Redis进行更高级的缓存

## 贡献

欢迎提交问题和拉取请求！

## 许可证

MIT 