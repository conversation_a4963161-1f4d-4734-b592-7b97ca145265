from flask import Blueprint, jsonify, render_template, request, current_app, redirect
from marshmallow import Schema, fields, validate, ValidationError
from sqlalchemy import or_
import requests
from app import db, cache
from app.models.keyword import Keyword

keyword_bp = Blueprint('keyword', __name__)

# Schema用于验证和序列化
class KeywordSchema(Schema):
    id = fields.Int(dump_only=True)
    content = fields.Str(required=True, validate=validate.Length(min=1, max=255))
    category = fields.Str(required=True, validate=validate.OneOf([
        'content', 'nickname', 'collection', 'competitor_block', 
        'competitor_auction', 'competitor_asset', 'best_block',
        'auction_block','assets_block','taobao_block','taobao_baohan','collect_gq_kw',
        'collect_gq_tel', 'collect_gq_userid'
    ]))
    status = fields.Boolean(missing=True)

keyword_schema = KeywordSchema()
keywords_schema = KeywordSchema(many=True)

@keyword_bp.route('/')
def index():
    """主页"""
    return render_template('index.html')

@keyword_bp.route('/keywords')
def keywords_page():
    """关键词管理页面"""
    categories = [
        {'value': 'content', 'text': '内容关键词'},
        {'value': 'nickname', 'text': '昵称关键词'},
        {'value': 'collection', 'text': '采集关键词'},
        {'value': 'competitor_block', 'text': '竞品屏蔽供求关键词'},
        {'value': 'competitor_auction', 'text': '竞品屏蔽拍卖关键词'},
        {'value': 'competitor_asset', 'text': '竞品屏蔽资产关键词'},
        {'value': 'best_block', 'text': '你最棒屏蔽关键词'},
        {'value': 'auction_block', 'text': '拍卖采集屏蔽关键词'},
        {'value': 'assets_block', 'text': '资产采集屏蔽关键词'},
        {'value': 'taobao_block', 'text': '淘宝采集屏蔽关键词'},
        {'value': 'taobao_baohan', 'text': '淘宝采集包含关键词'},
        {'value': 'collect_gq_kw', 'text': '供求采集屏蔽关键词'},
        {'value': 'collect_gq_tel', 'text': '供求采集屏蔽手机号'},
        {'value': 'collect_gq_userid', 'text': '供求采集屏蔽用户ID'},
    ]
    per_page_options = current_app.config['ITEMS_PER_PAGE']
    update_url = "http://192.168.1.115:5222/update"
    return render_template('keywords.html', categories=categories, 
                          per_page_options=per_page_options,
                          update_url=update_url)

@keyword_bp.route('/api/keywords', methods=['GET'])
@cache.cached(timeout=60, query_string=True)
def get_keywords():
    """获取关键词列表"""
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', current_app.config['DEFAULT_PER_PAGE'], type=int)
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    status = request.args.get('status', None, type=lambda x: x.lower() == 'true' if x is not None else None)
    
    # 构建查询
    query = Keyword.query
    
    if search:
        query = query.filter(Keyword.content.ilike(f'%{search}%'))
    
    if category:
        query = query.filter(Keyword.category == category)
    
    if status is not None:
        query = query.filter(Keyword.status == status)
    
    # 执行分页查询
    pagination = query.order_by(Keyword.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False)
    
    # 处理结果
    keywords = pagination.items
    result = {
        'items': [keyword.to_dict() for keyword in keywords],
        'total': pagination.total,
        'pages': pagination.pages,
        'page': pagination.page,
        'per_page': pagination.per_page
    }
    
    return jsonify(result)

@keyword_bp.route('/api/update-data', methods=['GET'])
def update_data():
    """从外部源更新数据"""
    try:
        # 从指定URL获取数据
        response = requests.get("http://192.168.1.115:5222/update", timeout=10)
        response.raise_for_status()
        
        # 清除缓存
        cache.clear()
        
        return jsonify({
            'success': True,
            'message': '数据更新成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'数据更新失败: {str(e)}'
        }), 500

@keyword_bp.route('/api/keywords', methods=['POST'])
def create_keyword():
    """创建新关键词"""
    try:
        # 验证数据
        data = keyword_schema.load(request.json)
        
        # 检查是否存在相同内容和分类的关键词   没有区分大小写
        # existing = Keyword.query.filter_by(
        #     content=data['content'],
        #     category=data['category']
        # ).first()

        from sqlalchemy import func  # 可以区分大小写
        existing = Keyword.query.filter(
            func.binary(Keyword.content) == data['content'],  # 强制区分大小写
            Keyword.category == data['category']
        ).first()
        
        if existing:
            return jsonify({'error': '该分类下已存在相同关键词'}), 400
        
        # 创建新关键词
        keyword = Keyword(
            content=data['content'],
            category=data['category'],
            status=data['status']
        )
        
        db.session.add(keyword)
        db.session.commit()
        
        # 清除缓存
        cache.clear()
        
        return jsonify(keyword.to_dict()), 201
    
    except ValidationError as err:
        return jsonify({'error': err.messages}), 400

@keyword_bp.route('/api/keywords/<int:id>', methods=['PUT'])
def update_keyword(id):
    """更新关键词"""
    keyword = Keyword.query.get_or_404(id)
    
    try:
        # 验证数据
        data = keyword_schema.load(request.json, partial=True)
        
        # 如果修改了内容或分类，检查是否存在相同内容和分类的关键词
        if ('content' in data or 'category' in data) and \
           data.get('content', keyword.content) != keyword.content or \
           data.get('category', keyword.category) != keyword.category:

            # 不能区分大小写
            # existing = Keyword.query.filter_by(
            # content=data.get('content', keyword.content),
            # category=data.get('category', keyword.category)
            # ).filter(Keyword.id != id).first()

            from sqlalchemy import func  # 可以区分大小写
            existing = Keyword.query.filter(
                func.binary(Keyword.content) == data.get('content', keyword.content),
                Keyword.category == data.get('category', keyword.category),
                Keyword.id != id
            ).first()

            if existing:
                return jsonify({'error': '该分类下已存在相同关键词'}), 400
        
        # 更新关键词
        for key, value in data.items():
            setattr(keyword, key, value)
        
        db.session.commit()
        
        # 清除缓存
        cache.clear()
        
        return jsonify(keyword.to_dict())
    
    except ValidationError as err:
        return jsonify({'error': err.messages}), 400

@keyword_bp.route('/api/keywords/<int:id>', methods=['DELETE'])
def delete_keyword(id):
    """删除关键词"""
    keyword = Keyword.query.get_or_404(id)
    
    db.session.delete(keyword)
    db.session.commit()
    
    # 清除缓存
    cache.clear()
    
    return jsonify({'message': '关键词已删除'}), 200

@keyword_bp.route('/api/keywords/batch', methods=['PUT'])
def batch_update_keywords():
    """批量更新关键词状态"""
    data = request.json
    
    if not data or 'ids' not in data or 'status' not in data:
        return jsonify({'error': '缺少必要参数'}), 400
    
    ids = data['ids']
    status = data['status']
    
    if not isinstance(ids, list) or not ids:
        return jsonify({'error': 'ids必须是非空数组'}), 400
    
    if not isinstance(status, bool):
        return jsonify({'error': 'status必须是布尔值'}), 400
    
    # 更新关键词状态
    Keyword.query.filter(Keyword.id.in_(ids)).update(
        {Keyword.status: status}, synchronize_session=False)
    
    db.session.commit()
    
    # 清除缓存
    cache.clear()
    
    return jsonify({'message': f'已成功更新{len(ids)}个关键词的状态'})

@keyword_bp.route('/api/keywords/batch', methods=['DELETE'])
def batch_delete_keywords():
    """批量删除关键词"""
    data = request.json
    
    if not data or 'ids' not in data:
        return jsonify({'error': '缺少必要参数'}), 400
    
    ids = data['ids']
    
    if not isinstance(ids, list) or not ids:
        return jsonify({'error': 'ids必须是非空数组'}), 400
    
    # 删除关键词
    count = Keyword.query.filter(Keyword.id.in_(ids)).delete(synchronize_session=False)
    
    db.session.commit()
    
    # 清除缓存
    cache.clear()
    
    return jsonify({'message': f'已成功删除{count}个关键词'}) 
