@echo off
chcp 65001
echo ===== 关键词管理系统数据库配置工具 =====

echo [信息] 将创建配置文件.env...

set /p DB_USERNAME=请输入MySQL用户名(默认root): 
if "%DB_USERNAME%"=="" set DB_USERNAME=root

set /p DB_PASSWORD=请输入MySQL密码: 

set /p DB_HOST=请输入MySQL主机地址(默认localhost): 
if "%DB_HOST%"=="" set DB_HOST=localhost

set /p DB_PORT=请输入MySQL端口(默认3306): 
if "%DB_PORT%"=="" set DB_PORT=3306

set /p DB_NAME=请输入MySQL数据库名(默认keyword_db): 
if "%DB_NAME%"=="" set DB_NAME=keyword_db

echo FLASK_APP=app.py> .env
echo FLASK_ENV=development>> .env
echo SECRET_KEY=hard-to-guess-string-for-keyword-app>> .env
echo.>> .env
echo # 数据库连接配置>> .env
echo DB_USERNAME=%DB_USERNAME%>> .env
echo DB_PASSWORD=%DB_PASSWORD%>> .env
echo DB_HOST=%DB_HOST%>> .env
echo DB_PORT=%DB_PORT%>> .env
echo DB_NAME=%DB_NAME%>> .env
echo.>> .env
echo # 完整的数据库连接URL>> .env
echo DEV_DATABASE_URL=mysql+pymysql://%DB_USERNAME%:%DB_PASSWORD%@%DB_HOST%:%DB_PORT%/%DB_NAME%>> .env

echo [信息] 配置文件.env已创建。

echo [警告] 请确保您已手动创建了MySQL数据库，否则应用将无法连接！
echo.
echo [信息] 数据库创建指南：
echo 1. 使用MySQL客户端（MySQL Workbench、phpMyAdmin等）
echo 2. 以配置的用户名和密码登录MySQL
echo 3. 执行以下SQL语句创建数据库：
echo    CREATE DATABASE %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
echo.
echo [信息] 您是否已创建了数据库？(y/n)
set /p DB_CREATED=

if /i "%DB_CREATED%"=="n" (
    echo [提示] 请创建数据库后再运行start.bat
    echo [提示] 或者您可以运行db_manual_setup.bat获取详细指南
    pause
    exit /b 1
)

echo [信息] 配置完成，现在可以运行start.bat启动应用了。
pause 