#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库连接测试脚本
用于测试MySQL数据库连接是否正常，并验证连接池配置
"""

import os
import sys
import time
from sqlalchemy import create_engine, text
from sqlalchemy.pool import QueuePool
from app import create_app, db
from app.models.keyword import Keyword

def test_basic_connection():
    """测试基本数据库连接"""
    print("=" * 50)
    print("测试基本数据库连接...")
    
    try:
        app = create_app('development')
        with app.app_context():
            # 测试数据库连接
            result = db.session.execute(text('SELECT 1 as test'))
            test_value = result.scalar()
            print(f"✅ 基本连接测试成功: {test_value}")
            
            # 测试表查询
            count = Keyword.query.count()
            print(f"✅ 关键词表查询成功，共有 {count} 条记录")
            
            return True
    except Exception as e:
        print(f"❌ 基本连接测试失败: {e}")
        return False

def test_connection_pool():
    """测试连接池配置"""
    print("=" * 50)
    print("测试连接池配置...")
    
    try:
        app = create_app('development')
        with app.app_context():
            engine = db.engine
            pool = engine.pool
            
            print(f"连接池类型: {type(pool).__name__}")
            print(f"连接池大小: {pool.size()}")
            print(f"当前连接数: {pool.checkedin()}")
            print(f"已检出连接数: {pool.checkedout()}")
            print(f"溢出连接数: {pool.overflow()}")
            print(f"无效连接数: {pool.invalidated()}")
            
            # 测试连接池预检查
            if hasattr(pool, '_pre_ping'):
                print(f"预检查启用: {pool._pre_ping}")
            
            print("✅ 连接池配置检查完成")
            return True
    except Exception as e:
        print(f"❌ 连接池测试失败: {e}")
        return False

def test_connection_recovery():
    """测试连接恢复能力"""
    print("=" * 50)
    print("测试连接恢复能力...")
    
    try:
        app = create_app('development')
        with app.app_context():
            # 执行多次查询，模拟长时间使用
            for i in range(5):
                result = db.session.execute(text('SELECT NOW() as current_time'))
                current_time = result.scalar()
                print(f"查询 {i+1}: {current_time}")
                
                # 模拟一些延迟
                time.sleep(1)
            
            print("✅ 连接恢复测试成功")
            return True
    except Exception as e:
        print(f"❌ 连接恢复测试失败: {e}")
        return False

def test_mysql_variables():
    """检查MySQL服务器变量"""
    print("=" * 50)
    print("检查MySQL服务器变量...")
    
    try:
        app = create_app('development')
        with app.app_context():
            # 检查重要的MySQL变量
            variables_to_check = [
                'wait_timeout',
                'interactive_timeout',
                'max_connections',
                'max_allowed_packet'
            ]
            
            for var in variables_to_check:
                result = db.session.execute(text(f"SHOW VARIABLES LIKE '{var}'"))
                row = result.fetchone()
                if row:
                    print(f"{var}: {row[1]}")
                else:
                    print(f"{var}: 未找到")
            
            print("✅ MySQL变量检查完成")
            return True
    except Exception as e:
        print(f"❌ MySQL变量检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始数据库连接诊断...")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    tests = [
        test_basic_connection,
        test_connection_pool,
        test_mysql_variables,
        test_connection_recovery,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据库连接配置正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查数据库配置。")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
