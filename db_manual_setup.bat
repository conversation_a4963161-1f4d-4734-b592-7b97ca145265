@echo off
chcp 65001
echo ===== 关键词管理系统 - 手动数据库设置指南 =====
echo.
echo [提示] 您需要手动创建MySQL数据库，请按照以下步骤操作：
echo.
echo 1. 确保MySQL服务器已安装并运行
echo 2. 使用MySQL客户端（如MySQL Workbench、phpMyAdmin或命令行）连接到您的MySQL服务器
echo 3. 执行以下SQL语句创建数据库：
echo.
echo    CREATE DATABASE keyword_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
echo.
echo 4. 确保您在.env文件中配置的用户有权限访问此数据库
echo.
echo [信息] 完成上述步骤后，您可以按任意键继续...
pause

echo [信息] 现在将创建环境配置文件...

set /p DB_USERNAME=请输入MySQL用户名(默认root): 
if "%DB_USERNAME%"=="" set DB_USERNAME=root

set /p DB_PASSWORD=请输入MySQL密码: 

set /p DB_HOST=请输入MySQL主机地址(默认localhost): 
if "%DB_HOST%"=="" set DB_HOST=localhost

set /p DB_PORT=请输入MySQL端口(默认3306): 
if "%DB_PORT%"=="" set DB_PORT=3306

set /p DB_NAME=请输入您创建的MySQL数据库名(默认keyword_db): 
if "%DB_NAME%"=="" set DB_NAME=keyword_db

echo FLASK_APP=app.py> .env
echo FLASK_ENV=development>> .env
echo SECRET_KEY=hard-to-guess-string-for-keyword-app>> .env
echo.>> .env
echo # 数据库连接配置>> .env
echo DB_USERNAME=%DB_USERNAME%>> .env
echo DB_PASSWORD=%DB_PASSWORD%>> .env
echo DB_HOST=%DB_HOST%>> .env
echo DB_PORT=%DB_PORT%>> .env
echo DB_NAME=%DB_NAME%>> .env
echo.>> .env
echo # 完整的数据库连接URL>> .env
echo DEV_DATABASE_URL=mysql+pymysql://%DB_USERNAME%:%DB_PASSWORD%@%DB_HOST%:%DB_PORT%/%DB_NAME%>> .env

echo [信息] 配置文件.env已创建。
echo [信息] 配置完成，现在可以运行start.bat启动应用了。
pause 