{% extends "base.html" %}

{% block title %}首页 - 关键词管理系统{% endblock %}

{% block header %}欢迎使用关键词管理系统{% endblock %}

{% block content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h5 class="card-title text-primary">系统介绍</h5>
                <p class="card-text">
                    这是一个高性能的关键词管理系统，支持多种关键词类型管理，包括内容关键词、昵称关键词、采集关键词、竞品屏蔽关键词、竞品屏蔽拍卖关键词、竞品屏蔽资产关键词和你最棒屏蔽关键词。系统可以同时支持数百人使用而不卡顿。
                </p>
                <p class="card-text">
                    本系统具有以下特点：
                </p>
                <ul>
                    <li>支持不同类型关键词管理</li>
                    <li>支持关键词状态管理（启用/禁用）</li>
                    <li>支持关键词搜索筛选</li>
                    <li>支持关键词批量操作</li>
                    <li>支持多人同时操作</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white shadow-sm">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-file-alt me-2"></i>内容关键词
                </h5>
                <p class="card-text">管理内容相关的关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=content" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
				<a href="http://192.168.1.119:5900/api/xyupdatedata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">
                            <i class="fas fa-sync-alt me-1"></i>数据更新
                        </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white shadow-sm">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-user-tag me-2"></i>昵称关键词
                </h5>
                <p class="card-text">管理用户昵称相关的关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=nickname" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white shadow-sm">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-spider me-2"></i>采集关键词
                </h5>
                <p class="card-text">管理数据采集相关的关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=collection" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-danger text-white shadow-sm">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-shield-alt me-2"></i>竞品屏蔽供求关键词
                </h5>
                <p class="card-text">管理竞品屏蔽相关的关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=competitor_block" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
				<a href="http://192.168.1.119:5800/api/updatejpgqdata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">
                            <i class="fas fa-sync-alt me-1"></i>数据更新
                        </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-warning text-dark shadow-sm">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-gavel me-2"></i>竞品屏蔽拍卖关键词
                </h5>
                <p class="card-text">管理竞品屏蔽拍卖相关的关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=competitor_auction" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
				<a href="http://192.168.1.119:5800/api/updatejppmdata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">
                            <i class="fas fa-sync-alt me-1"></i>数据更新
                        </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-secondary text-white shadow-sm">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-landmark me-2"></i>竞品屏蔽资产关键词
                </h5>
                <p class="card-text">管理竞品屏蔽资产相关的关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=competitor_asset" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
				<a href="http://192.168.1.119:5800/api/updatejpzcdata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">
                            <i class="fas fa-sync-alt me-1"></i>数据更新
                        </a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-purple text-white shadow-sm" style="background-color: #8a2be2;">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-crown me-2"></i>你最棒屏蔽关键词
                </h5>
                <p class="card-text">管理你最棒屏蔽相关的关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=best_block" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
				<a href="http://192.168.1.119:5800/api/updatenzbdata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">
                            <i class="fas fa-sync-alt me-1"></i>数据更新
                        </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-purple text-white shadow-sm" style="background-color: #0000cd;">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-crown me-2"></i>拍卖采集屏蔽关键词
                </h5>
                <p class="card-text">管理拍卖采集屏蔽相关的关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=auction_block" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
				<a href="http://192.168.1.119:5800/api/updatenauctiondata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">
                            <i class="fas fa-sync-alt me-1"></i> 数据更新
                        </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-purple text-white shadow-sm" style="background-color: #20b2aa;">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-crown me-2"></i>资产采集屏蔽关键词
                </h5>
                <p class="card-text">管理资产采集屏蔽相关的关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=assets_block" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
				<a href="http://192.168.1.119:5800/api/updateassetsdata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">
                            <i class="fas fa-sync-alt me-1"></i> 数据更新
                        </a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-purple text-white shadow-sm" style="background-color: #2E8B57;">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-crown me-2"></i>淘宝采集屏蔽关键词
                </h5>
                <p class="card-text">管理淘宝采集屏蔽关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=taobao_block" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-purple text-white shadow-sm" style="background-color: #FF4500;">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-crown me-2"></i>淘宝采集包含关键词
                </h5>
                <p class="card-text">管理淘宝采集包含关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=taobao_baohan" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
            </div>
        </div>
    </div>
{#    <div class="col-md-4">#}
{#        <div class="card bg-purple text-white shadow-sm" style="background-color: #20b2aa;">#}
{#            <div class="card-body">#}
{#                <h5 class="card-title">#}
{#                    <i class="fas fa-crown me-2"></i>资产采集屏蔽关键词#}
{#                </h5>#}
{#                <p class="card-text">管理资产采集屏蔽相关的关键词</p>#}
{#                <a href="{{ url_for('keyword.keywords_page') }}?category=assets_block" class="btn btn-light">#}
{#                    <i class="fas fa-arrow-right"></i> 前往管理#}
{#                </a>#}
{#				<a href="http://192.168.1.119:5800/api/updateassetsdata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">#}
{#                            <i class="fas fa-sync-alt me-1"></i> 数据更新#}
{#                        </a>#}
{#            </div>#}
{#        </div>#}
{#    </div>#}
</div>
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-purple text-white shadow-sm" style="background-color:#1C86EE;">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-crown me-2"></i>供求采集屏蔽所有关键词
                </h5>
                <p class="card-text">管理供求采集屏蔽关键词</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=collect_gq_kw" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
                <a href="http://192.168.1.119:5800/api/updateqgdata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">
                            <i class="fas fa-sync-alt me-1"></i> 数据更新
                        </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-purple text-white shadow-sm" style="background-color:#8B4513;">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-crown me-2"></i>供求采集屏蔽所有手机号
                </h5>
                <p class="card-text">管理供求采集屏蔽手机号</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=collect_gq_tel" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
                <a href="http://192.168.1.119:5800/api/updatengqmobiledata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">
                            <i class="fas fa-sync-alt me-1"></i> 数据更新
                        </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-purple text-white shadow-sm" style="background-color: #20b2aa;">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-crown me-2"></i>供求采集屏蔽用户58ID
                </h5>
                <p class="card-text">管理供求采集屏蔽用户ID</p>
                <a href="{{ url_for('keyword.keywords_page') }}?category=collect_gq_userid" class="btn btn-light">
                    <i class="fas fa-arrow-right"></i> 前往管理
                </a>
				<a href="http://192.168.1.119:5800/api/updatengquseriddata" target="_blank" class="btn btn-update ms-2" style="background-color: #FFFFCD;">
                            <i class="fas fa-sync-alt me-1"></i> 数据更新
                        </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
