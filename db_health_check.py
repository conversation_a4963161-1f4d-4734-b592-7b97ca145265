#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库健康检查脚本
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text
import os
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)

# 配置数据库连接
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DEV_DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 3600,
    'pool_timeout': 30,
    'max_overflow': 20,
    'connect_args': {
        'connect_timeout': 60,
        'read_timeout': 30,
        'write_timeout': 30,
        'charset': 'utf8mb4',
        'autocommit': False,
    }
}

db = SQLAlchemy(app)

def check_database():
    """检查数据库连接"""
    with app.app_context():
        try:
            # 测试连接
            result = db.session.execute(text('SELECT 1 as test'))
            test_value = result.scalar()
            print(f"✅ 数据库连接正常: {test_value}")
            
            # 检查表
            result = db.session.execute(text("SHOW TABLES LIKE 'keywords'"))
            table_exists = result.fetchone()
            if table_exists:
                result = db.session.execute(text("SELECT COUNT(*) FROM keywords"))
                count = result.scalar()
                print(f"✅ keywords表存在，记录数: {count}")
            else:
                print("⚠️  keywords表不存在")
            
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

if __name__ == '__main__':
    check_database()
