<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="58">
            <item index="0" class="java.lang.String" itemvalue="protobuf" />
            <item index="1" class="java.lang.String" itemvalue="shapely" />
            <item index="2" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="3" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="4" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="5" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="6" class="java.lang.String" itemvalue="unidecode" />
            <item index="7" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="8" class="java.lang.String" itemvalue="torchvision" />
            <item index="9" class="java.lang.String" itemvalue="markupsafe" />
            <item index="10" class="java.lang.String" itemvalue="fsspec" />
            <item index="11" class="java.lang.String" itemvalue="appdirs" />
            <item index="12" class="java.lang.String" itemvalue="qudida" />
            <item index="13" class="java.lang.String" itemvalue="sentry-sdk" />
            <item index="14" class="java.lang.String" itemvalue="certifi" />
            <item index="15" class="java.lang.String" itemvalue="multiprocess" />
            <item index="16" class="java.lang.String" itemvalue="gitpython" />
            <item index="17" class="java.lang.String" itemvalue="pyparsing" />
            <item index="18" class="java.lang.String" itemvalue="sympy" />
            <item index="19" class="java.lang.String" itemvalue="tifffile" />
            <item index="20" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="21" class="java.lang.String" itemvalue="attrs" />
            <item index="22" class="java.lang.String" itemvalue="jinja2" />
            <item index="23" class="java.lang.String" itemvalue="fonttools" />
            <item index="24" class="java.lang.String" itemvalue="pytorch-lightning" />
            <item index="25" class="java.lang.String" itemvalue="flatbuffers" />
            <item index="26" class="java.lang.String" itemvalue="imageio" />
            <item index="27" class="java.lang.String" itemvalue="matplotlib" />
            <item index="28" class="java.lang.String" itemvalue="scikit-image" />
            <item index="29" class="java.lang.String" itemvalue="datasets" />
            <item index="30" class="java.lang.String" itemvalue="numpy" />
            <item index="31" class="java.lang.String" itemvalue="opencv-python-headless" />
            <item index="32" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="33" class="java.lang.String" itemvalue="seaborn" />
            <item index="34" class="java.lang.String" itemvalue="zipp" />
            <item index="35" class="java.lang.String" itemvalue="lightning-utilities" />
            <item index="36" class="java.lang.String" itemvalue="wandb" />
            <item index="37" class="java.lang.String" itemvalue="urllib3" />
            <item index="38" class="java.lang.String" itemvalue="pyarrow" />
            <item index="39" class="java.lang.String" itemvalue="scipy" />
            <item index="40" class="java.lang.String" itemvalue="opencv-python" />
            <item index="41" class="java.lang.String" itemvalue="tzdata" />
            <item index="42" class="java.lang.String" itemvalue="dill" />
            <item index="43" class="java.lang.String" itemvalue="packaging" />
            <item index="44" class="java.lang.String" itemvalue="torch" />
            <item index="45" class="java.lang.String" itemvalue="lazy-loader" />
            <item index="46" class="java.lang.String" itemvalue="albumentations" />
            <item index="47" class="java.lang.String" itemvalue="pandas" />
            <item index="48" class="java.lang.String" itemvalue="tqdm" />
            <item index="49" class="java.lang.String" itemvalue="importlib-resources" />
            <item index="50" class="java.lang.String" itemvalue="pillow" />
            <item index="51" class="java.lang.String" itemvalue="aiohttp" />
            <item index="52" class="java.lang.String" itemvalue="multidict" />
            <item index="53" class="java.lang.String" itemvalue="pytz" />
            <item index="54" class="java.lang.String" itemvalue="cnstd" />
            <item index="55" class="java.lang.String" itemvalue="cookiesparser" />
            <item index="56" class="java.lang.String" itemvalue="httpx" />
            <item index="57" class="java.lang.String" itemvalue="PyExecJS" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>