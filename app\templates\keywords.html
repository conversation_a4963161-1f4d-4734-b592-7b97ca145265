{% extends "base.html" %}

{% block title %}关键词管理 - 关键词管理系统{% endblock %}

{% block header %}关键词管理{% endblock %}

{% block styles %}
<style>
    .keyword-table th, .keyword-table td {
        vertical-align: middle;
    }
    .badge-status {
        min-width: 80px;
    }
    .action-buttons .btn {
        margin-right: 5px;
    }
    #keywordForm {
        border-radius: 15px;
        background: linear-gradient(145deg, #f8f9fa, #e9ecef);
        box-shadow: 5px 5px 10px #d1d9e6, -5px -5px 10px #ffffff;
    }
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    .loading-spinner {
        width: 3rem;
        height: 3rem;
    }
    .form-control, .form-select, .btn {
        border-radius: 10px;
    }
    .btn-primary {
        background: linear-gradient(145deg, #7b68ee, #9370db);
        border: none;
    }
    .table {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0,0,0,.05);
    }
    .table thead th {
        background-color: #f8f9fa;
        font-weight: 600;
    }
    .page-item.active .page-link {
        background-color: #7b68ee;
        border-color: #7b68ee;
    }
    .page-link {
        color: #7b68ee;
    }
    .btn-update {
        background: linear-gradient(145deg, #ff9a9e, #fad0c4);
        color: #333;
        border: none;
        transition: all 0.3s;
    }
    .btn-update:hover {
        background: linear-gradient(145deg, #fad0c4, #ff9a9e);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(255, 154, 158, 0.3);
        color: #333;
    }
</style>
{% endblock %}

{% block content %}
<!-- 搜索和表单部分 -->
<div class="row mb-4">
    <div class="col-lg-12">
        <div class="card p-3 shadow-sm" id="keywordForm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="fas fa-filter me-2"></i>关键词管理
                </h5>
                
                <div class="row g-3">
                    <!-- 搜索区域 -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="searchInput" class="form-label">搜索关键词</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="输入关键词内容">
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="categoryFilter" class="form-label">关键词分类</label>
                            <select class="form-select" id="categoryFilter">
                                <option value="">全部分类</option>
                                {% for category in categories %}
                                <option value="{{ category.value }}">{{ category.text }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="statusFilter" class="form-label">状态筛选</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="perPageSelector" class="form-label">每页显示</label>
                            <select class="form-select" id="perPageSelector">
                                {% for option in per_page_options %}
                                <option value="{{ option }}">{{ option }}条/页</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between mt-3">
                    <div>
                        <button type="button" class="btn btn-primary" id="searchBtn">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <!--<a href="{{ update_url }}" target="_blank" class="btn btn-update ms-2">-->
                        <!--<a href="http://192.168.1.119:5800/updatedata" target="_blank" class="btn btn-update ms-2">
                            <i class="fas fa-sync-alt me-1"></i>数据更新
                        </a>-->
                    </div>
                    
                    <div>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#keywordModal">
                            <i class="fas fa-plus me-1"></i>添加关键词
                        </button>
                        <div class="btn-group ms-2">
                            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                批量操作
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" id="batchEnableBtn">批量启用</a></li>
                                <li><a class="dropdown-item" href="#" id="batchDisableBtn">批量禁用</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" id="batchDeleteBtn">批量删除</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 关键词表格 -->
<div class="row">
    <div class="col-lg-12">
        <div class="table-responsive">
            <table class="table table-hover keyword-table">
                <thead>
                    <tr>
                        <th width="40">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAllCheckbox">
                                <label class="form-check-label" for="selectAllCheckbox"></label>
                            </div>
                        </th>
                        <th width="60">ID</th>
                        <th>关键词内容</th>
                        <th width="140">分类</th>
                        <th width="120">状态</th>
                        <th width="180">创建时间</th>
                        <th width="180">更新时间</th>
                        <th width="140">操作</th>
                    </tr>
                </thead>
                <tbody id="keywordTableBody">
                    <!-- 表格数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
        
        <!-- 分页器 -->
        <nav aria-label="Page navigation" class="mt-3">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <span id="paginationInfo">共 <span id="totalItems">0</span> 条记录，当前第 <span id="currentPage">1</span>/<span id="totalPages">1</span> 页</span>
                </div>
                <ul class="pagination" id="pagination">
                    <!-- 分页将通过JavaScript动态加载 -->
                </ul>
            </div>
        </nav>
    </div>
</div>

<!-- 关键词添加/编辑模态框 -->
<div class="modal fade" id="keywordModal" tabindex="-1" aria-labelledby="keywordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="keywordModalLabel">添加关键词</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="keywordModalForm">
                    <input type="hidden" id="keywordId">
                    <div class="mb-3">
                        <label for="keywordContent" class="form-label">关键词内容</label>
                        <input type="text" class="form-control" id="keywordContent" required>
                    </div>
                    <div class="mb-3">
                        <label for="keywordCategory" class="form-label">关键词分类</label>
                        <select class="form-select" id="keywordCategory" required>
                            {% for category in categories %}
                            <option value="{{ category.value }}">{{ category.text }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="keywordStatus" checked>
                        <label class="form-check-label" for="keywordStatus">启用状态</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveKeywordBtn">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除选中的关键词吗？此操作不可恢复！
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 加载中遮罩 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="spinner-border text-primary loading-spinner" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // 全局变量，包括更新URL
    let currentPage = 1;
    let perPage = 50;
    let totalPages = 1;
    let selectedIds = [];
    let deleteId = null;
    let isEditMode = false;
    let updateUrl = "{{ update_url }}";
    
    // 初始化
    $(document).ready(function() {
        // 初始化每页显示数量
        perPage = parseInt($('#perPageSelector').val());
        
        // 加载关键词数据
        loadKeywords();
        
        // 绑定事件
        bindEvents();
        
        // 检查URL参数
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('category')) {
            $('#categoryFilter').val(urlParams.get('category'));
            {#$('#keywordCategory').val(urlParams.get('category'));#}
            loadKeywords();
        }

        // 当添加关键词按钮被点击时，设置模态框的分类为当前筛选值
        $('[data-bs-target="#keywordModal"]').on('click', function() {
            // 获取当前列表的分类筛选值
            const currentCategory = $('#categoryFilter').val();

            // 设置到模态框的分类选择框
            $('#keywordCategory').val(currentCategory);
        });
    });
    
    // 绑定事件处理函数
    function bindEvents() {
        // 搜索按钮点击
        $('#searchBtn').on('click', function() {
            currentPage = 1;
            loadKeywords();
        });
        
        // 每页显示数量改变
        $('#perPageSelector').on('change', function() {
            perPage = parseInt($(this).val());
            currentPage = 1;
            loadKeywords();
        });
        
        // 全选/取消全选
        $('#selectAllCheckbox').on('change', function() {
            const isChecked = $(this).prop('checked');
            $('.keyword-checkbox').prop('checked', isChecked);
            updateSelectedIds();
        });
        
        // 保存关键词按钮点击
        $('#saveKeywordBtn').on('click', saveKeyword);
        
        // 批量操作按钮点击
        $('#batchEnableBtn').on('click', function() { batchUpdateStatus(true); });
        $('#batchDisableBtn').on('click', function() { batchUpdateStatus(false); });
        $('#batchDeleteBtn').on('click', function() {
            if (selectedIds.length === 0) {
                alert('请先选择要删除的关键词！');
                return;
            }
            $('#deleteConfirmModal').modal('show');
        });
        
        // 确认删除按钮点击
        $('#confirmDeleteBtn').on('click', function() {
            if (deleteId !== null) {
                deleteKeyword(deleteId);
            } else {
                batchDeleteKeywords();
            }
            $('#deleteConfirmModal').modal('hide');
        });
        
        // 添加/编辑模态框关闭时重置表单
        $('#keywordModal').on('hidden.bs.modal', function() {
            $('#keywordModalForm')[0].reset();
            $('#keywordId').val('');
            $('#keywordModalLabel').text('添加关键词');
            isEditMode = false;
        });
    }
    
    // 加载关键词数据
    function loadKeywords() {
        showLoading();
        
        // 获取筛选参数
        const search = $('#searchInput').val();
        const category = $('#categoryFilter').val();
        const status = $('#statusFilter').val();
        
        // 构建请求参数
        const params = {
            page: currentPage,
            per_page: perPage
        };
        
        if (search) params.search = search;
        if (category) params.category = category;
        if (status !== '') params.status = status;
        
        // 发送AJAX请求
        axios.get('/api/keywords', { params: params })
            .then(function(response) {
                renderKeywords(response.data);
                hideLoading();
            })
            .catch(function(error) {
                console.error('获取关键词失败:', error);
                alert('获取关键词失败：' + (error.response?.data?.error || error.message));
                hideLoading();
            });
    }
    
    // 渲染关键词表格
    function renderKeywords(data) {
        const keywords = data.items;
        const tableBody = $('#keywordTableBody');
        tableBody.empty();
        
        // 更新分页信息
        totalPages = data.pages;
        $('#totalItems').text(data.total);
        $('#currentPage').text(data.page);
        $('#totalPages').text(data.pages);
        
        // 渲染表格
        if (keywords.length === 0) {
            tableBody.html('<tr><td colspan="8" class="text-center">暂无数据</td></tr>');
        } else {
            keywords.forEach(function(keyword) {
                const statusBadge = keyword.status ? 
                    '<span class="badge bg-success badge-status">已启用</span>' : 
                    '<span class="badge bg-secondary badge-status">已禁用</span>';
                
                const categoryText = getCategoryText(keyword.category);
                
                const row = `
                    <tr>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input keyword-checkbox" type="checkbox" value="${keyword.id}">
                            </div>
                        </td>
                        <td>${keyword.id}</td>
                        <td>${keyword.content}</td>
                        <td>${categoryText}</td>
                        <td>${statusBadge}</td>
                        <td>${keyword.created_at}</td>
                        <td>${keyword.updated_at}</td>
                        <td class="action-buttons">
                            <button type="button" class="btn btn-sm btn-outline-primary edit-btn" data-id="${keyword.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-${keyword.status ? 'warning' : 'success'} toggle-btn" data-id="${keyword.id}" data-status="${keyword.status}">
                                <i class="fas fa-${keyword.status ? 'ban' : 'check'}"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="${keyword.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                
                tableBody.append(row);
            });
            
            // 绑定操作按钮事件
            $('.edit-btn').on('click', function() {
                const id = $(this).data('id');
                editKeyword(id, keywords.find(k => k.id === id));
            });
            
            $('.toggle-btn').on('click', function() {
                const id = $(this).data('id');
                const currentStatus = $(this).data('status');
                updateKeywordStatus(id, !currentStatus);
            });
            
            $('.delete-btn').on('click', function() {
                const id = $(this).data('id');
                deleteId = id;
                $('#deleteConfirmModal').modal('show');
            });
            
            // 复选框变化时更新选中ID列表
            $('.keyword-checkbox').on('change', updateSelectedIds);
        }
        
        // 渲染分页器
        renderPagination(data);
    }
    
    // 渲染分页器
    function renderPagination(data) {
        const pagination = $('#pagination');
        pagination.empty();
        
        // 如果只有一页，不显示分页器
        if (data.pages <= 1) {
            return;
        }
        
        // 上一页按钮
        const prevBtn = `
            <li class="page-item ${data.page === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${data.page - 1}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        `;
        pagination.append(prevBtn);
        
        // 页码按钮
        let startPage = Math.max(1, data.page - 2);
        let endPage = Math.min(data.pages, data.page + 2);
        
        // 确保显示5个页码按钮（如果有足够多的页）
        if (endPage - startPage < 4 && data.pages > 5) {
            if (startPage === 1) {
                endPage = Math.min(5, data.pages);
            } else if (endPage === data.pages) {
                startPage = Math.max(1, data.pages - 4);
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = `
                <li class="page-item ${data.page === i ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
            pagination.append(pageBtn);
        }
        
        // 下一页按钮
        const nextBtn = `
            <li class="page-item ${data.page === data.pages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${data.page + 1}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        `;
        pagination.append(nextBtn);
        
        // 绑定分页事件
        $('.page-link').on('click', function(e) {
            e.preventDefault();
            if ($(this).parent().hasClass('disabled')) {
                return;
            }
            
            currentPage = parseInt($(this).data('page'));
            loadKeywords();
        });
    }
    
    // 编辑关键词
    function editKeyword(id, keyword) {
        isEditMode = true;
        $('#keywordModalLabel').text('编辑关键词');
        $('#keywordId').val(id);
        $('#keywordContent').val(keyword.content);
        $('#keywordCategory').val(keyword.category);
        $('#keywordStatus').prop('checked', keyword.status);
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('keywordModal'));
        modal.show();
    }
    
    // 保存关键词（添加或更新）
    function saveKeyword() {
        const id = $('#keywordId').val();
        const content = $('#keywordContent').val();
        const category = $('#keywordCategory').val();
        const status = $('#keywordStatus').prop('checked');
        
        if (!content) {
            alert('请输入关键词内容！');
            return;
        }
        
        const keywordData = {
            content: content,
            category: category,
            status: status
        };
        
        showLoading();
        
        if (isEditMode) {
            // 更新关键词
            axios.put(`/api/keywords/${id}`, keywordData)
                .then(function(response) {
                    loadKeywords();
                    $('#keywordModal').modal('hide');
                })
                .catch(function(error) {
                    console.error('更新关键词失败:', error);
                    alert('更新关键词失败：' + (error.response?.data?.error || error.message));
                    hideLoading();
                });
        } else {
            // 添加关键词
            axios.post('/api/keywords', keywordData)
                .then(function(response) {
                    loadKeywords();
                    $('#keywordModal').modal('hide');
                })
                .catch(function(error) {
                    console.error('添加关键词失败:', error);
                    alert('添加关键词失败：' + (error.response?.data?.error || error.message));
                    hideLoading();
                });
        }
    }
    
    // 更新关键词状态
    function updateKeywordStatus(id, newStatus) {
        showLoading();
        
        axios.put(`/api/keywords/${id}`, { status: newStatus })
            .then(function(response) {
                loadKeywords();
            })
            .catch(function(error) {
                console.error('更新关键词状态失败:', error);
                alert('更新关键词状态失败：' + (error.response?.data?.error || error.message));
                hideLoading();
            });
    }
    
    // 删除关键词
    function deleteKeyword(id) {
        showLoading();
        
        axios.delete(`/api/keywords/${id}`)
            .then(function(response) {
                loadKeywords();
                deleteId = null;
            })
            .catch(function(error) {
                console.error('删除关键词失败:', error);
                alert('删除关键词失败：' + (error.response?.data?.error || error.message));
                hideLoading();
            });
    }
    
    // 批量更新状态
    function batchUpdateStatus(newStatus) {
        if (selectedIds.length === 0) {
            alert('请先选择要操作的关键词！');
            return;
        }
        
        showLoading();
        
        axios.put('/api/keywords/batch', { ids: selectedIds, status: newStatus })
            .then(function(response) {
                loadKeywords();
                selectedIds = [];
                $('#selectAllCheckbox').prop('checked', false);
            })
            .catch(function(error) {
                console.error('批量更新状态失败:', error);
                alert('批量更新状态失败：' + (error.response?.data?.error || error.message));
                hideLoading();
            });
    }
    
    // 批量删除关键词
    function batchDeleteKeywords() {
        if (selectedIds.length === 0) {
            alert('请先选择要删除的关键词！');
            return;
        }
        
        showLoading();
        
        axios.delete('/api/keywords/batch', { data: { ids: selectedIds } })
            .then(function(response) {
                loadKeywords();
                selectedIds = [];
                $('#selectAllCheckbox').prop('checked', false);
            })
            .catch(function(error) {
                console.error('批量删除关键词失败:', error);
                alert('批量删除关键词失败：' + (error.response?.data?.error || error.message));
                hideLoading();
            });
    }
    
    // 更新选中的ID列表
    function updateSelectedIds() {
        selectedIds = [];
        $('.keyword-checkbox:checked').each(function() {
            selectedIds.push(parseInt($(this).val()));
        });
        
        // 如果所有复选框都选中，则全选框也应该选中
        $('#selectAllCheckbox').prop('checked', 
            $('.keyword-checkbox').length > 0 && 
            $('.keyword-checkbox').length === $('.keyword-checkbox:checked').length);
    }
    
    // 获取分类文本
    function getCategoryText(category) {
        const categoryMap = {
            'content': '内容关键词',
            'nickname': '昵称关键词',
            'collection': '采集关键词',
            'competitor_block': '竞品屏蔽关键词',
            'competitor_auction': '竞品屏蔽拍卖关键词',
            'competitor_asset': '竞品屏蔽资产关键词',
            'best_block': '你最棒屏蔽关键词',
            'auction_block': '拍卖采集屏蔽关键词',
            'assets_block': '资产采集屏蔽关键词',
            'taobao_block': '淘宝采集屏蔽关键词',
            'taobao_baohan': '淘宝采集包含关键词',
            'collect_gq_kw': '供求采集屏蔽关键词',
            'collect_gq_tel': '供求采集屏蔽手机号',
            'collect_gq_userid': '供求采集屏蔽用户ID',
        };
        return categoryMap[category] || '未知分类';
    }
    
    // 显示加载中遮罩
    function showLoading() {
        $('#loadingOverlay').fadeIn(200);
    }
    
    // 隐藏加载中遮罩
    function hideLoading() {
        $('#loadingOverlay').fadeOut(200);
    }
    
    // 更新关键词数据
    // 注意：目前数据更新是通过点击链接在新窗口打开更新URL实现的
    // 此函数保留作为API方式更新数据的备选方案
    function updateKeywordData() {
        showLoading();

        axios.get('/api/update-data')
            .then(function(response) {
                if (response.data.success) {
                    alert('数据更新成功！');
                    loadKeywords(); // 重新加载关键词数据
                } else {
                    alert('数据更新失败：' + response.data.message);
                    hideLoading();
                }
            })
            .catch(function(error) {
                console.error('数据更新失败:', error);
                alert('数据更新失败：' + (error.response?.data?.error || error.message));
                hideLoading();
            });
    }
</script>
{% endblock %} 
