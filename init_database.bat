@echo off
chcp 65001
echo ===== 关键词管理系统 - 数据库表初始化 =====

REM 检查配置文件是否存在
if not exist .env (
    echo [警告] 未找到配置文件，将运行数据库配置助手...
    call db_setup.bat
)

REM 检查是否存在虚拟环境
if not exist venv (
    echo [信息] 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo [信息] 激活虚拟环境...
call venv\Scripts\activate

echo [信息] 开始初始化数据库表结构...

REM 初始化迁移目录(如果不存在)
if not exist migrations (
    echo [信息] 初始化数据库迁移...
    flask db init
    
    REM 如果上一步出错，可能是数据库连接问题
    if %ERRORLEVEL% NEQ 0 (
        echo [错误] 数据库初始化失败，可能是数据库连接参数错误。
        echo [信息] 请运行db_setup.bat重新配置数据库参数。
        pause
        exit /b 1
    )
)

REM 创建迁移脚本并应用迁移
echo [信息] 创建数据库迁移脚本...
flask db migrate -m "initial schema"
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 创建迁移脚本失败。
    pause
    exit /b 1
)

echo [信息] 应用数据库迁移...
flask db upgrade
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 应用迁移失败。
    pause
    exit /b 1
)

echo [信息] 是否创建测试数据？(y/n)
set /p CREATE_TEST_DATA=

if /i "%CREATE_TEST_DATA%"=="y" (
    echo [信息] 创建测试数据...
    flask create_test_data
)

echo [信息] 数据库表初始化完成，现在可以运行 start.bat 启动应用。
pause 