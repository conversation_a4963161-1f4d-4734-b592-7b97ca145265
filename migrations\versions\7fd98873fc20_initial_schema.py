"""initial schema

Revision ID: 7fd98873fc20
Revises: 
Create Date: 2025-05-07 22:53:03.705901

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7fd98873fc20'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('keywords',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content', sa.String(length=255), nullable=False, comment='关键词内容'),
    sa.Column('category', sa.Enum('content', 'nickname', 'collection', 'competitor_block'), nullable=False, comment='关键词分类'),
    sa.Column('status', sa.<PERSON>(), nullable=True, comment='关键词状态：启用/禁用'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.<PERSON>umn('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('keywords', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_keywords_content'), ['content'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('keywords', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_keywords_content'))

    op.drop_table('keywords')
    # ### end Alembic commands ###
