import os
from app import create_app, db
from app.models.keyword import Keyword
from flask_migrate import Migrate

# 创建应用实例
app = create_app(os.getenv('FLASK_ENV') or 'default')
migrate = Migrate(app, db)

@app.shell_context_processor
def make_shell_context():
    """为Python shell添加上下文数据"""
    return dict(db=db, Keyword=Keyword)

@app.cli.command()
def test():
    """运行单元测试"""
    import unittest
    tests = unittest.TestLoader().discover('tests')
    unittest.TextTestRunner(verbosity=2).run(tests)

@app.cli.command()
def deploy():
    """部署命令"""
    from flask_migrate import upgrade

    # 迁移数据库到最新版本
    upgrade()

@app.cli.command()
def create_test_data():
    """创建测试数据"""
    from random import choice, sample
    import string
    
    # 检查是否已有数据
    if Keyword.query.count() > 0:
        print('数据库中已有关键词数据，跳过创建测试数据')
        return

    # 创建关键词分类
    categories = ['content', 'nickname', 'collection', 'competitor_block']
    
    # 生成随机关键词
    def generate_random_word(length=5):
        return ''.join(choice(string.ascii_lowercase) for _ in range(length))
    
    # 批量创建测试数据
    test_data = []
    
    # 内容关键词
    for _ in range(50):
        word = generate_random_word(choice(range(3, 8)))
        test_data.append(Keyword(word, 'content', choice([True, False])))
    
    # 昵称关键词
    for _ in range(30):
        word = 'user_' + generate_random_word(choice(range(4, 10)))
        test_data.append(Keyword(word, 'nickname', choice([True, False])))
    
    # 采集关键词
    for _ in range(40):
        word = '#' + generate_random_word(choice(range(5, 12)))
        test_data.append(Keyword(word, 'collection', choice([True, True, False])))
    
    # 竞品屏蔽关键词
    for _ in range(20):
        word = 'competitor_' + generate_random_word(choice(range(3, 6)))
        test_data.append(Keyword(word, 'competitor_block', choice([True, True, True, False])))
    
    # 添加到数据库
    db.session.add_all(test_data)
    db.session.commit()
    
    print(f'成功创建 {len(test_data)} 条测试关键词数据')

if __name__ == '__main__':
    # 启动应用
    app.run(host='0.0.0.0', port=5300, debug=True) 