/**
 * 关键词管理系统主JavaScript文件
 */

// 确保DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 设置AJAX请求的默认头信息（CSRF保护）
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = csrfToken.content;
    }
    
    // 初始化移动端侧边栏切换功能
    initMobileSidebar();
    
    // 初始化表单验证
    initFormValidation();
    
    // 初始化工具提示
    initTooltips();
});

/**
 * 初始化移动端侧边栏
 */
function initMobileSidebar() {
    const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
    const sidebar = document.getElementById('sidebar');
    
    if (sidebarToggleBtn && sidebar) {
        sidebarToggleBtn.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
        
        // 点击外部区域关闭侧边栏
        document.addEventListener('click', function(event) {
            if (sidebar.classList.contains('show') && 
                !sidebar.contains(event.target) && 
                event.target !== sidebarToggleBtn) {
                sidebar.classList.remove('show');
            }
        });
    }
}

/**
 * 初始化表单验证
 */
function initFormValidation() {
    // 获取所有需要验证的表单
    const forms = document.querySelectorAll('.needs-validation');
    
    // 遍历表单绑定验证事件
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * 初始化工具提示
 */
function initTooltips() {
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    if (tooltipTriggerList.length > 0) {
        Array.from(tooltipTriggerList).forEach(tooltipTriggerEl => {
            new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型：success, warning, danger, info
 * @param {number} duration - 显示时长（毫秒）
 */
function showNotification(message, type = 'info', duration = 3000) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show notification-toast`;
    notification.setAttribute('role', 'alert');
    
    // 设置通知内容
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // 添加到页面
    const container = document.querySelector('.notification-container') || document.body;
    container.appendChild(notification);
    
    // 设置自动关闭
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, duration);
}

/**
 * 确认操作对话框
 * @param {string} message - 确认消息
 * @param {Function} callback - 确认后的回调函数
 * @param {string} confirmText - 确认按钮文本
 * @param {string} cancelText - 取消按钮文本
 */
function confirmAction(message, callback, confirmText = '确认', cancelText = '取消') {
    // 检查是否已存在模态框
    let modal = document.getElementById('confirmActionModal');
    
    // 如果不存在，创建新模态框
    if (!modal) {
        modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'confirmActionModal';
        modal.setAttribute('tabindex', '-1');
        modal.setAttribute('aria-labelledby', 'confirmActionModalLabel');
        modal.setAttribute('aria-hidden', 'true');
        
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="confirmActionModalLabel">确认操作</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="confirmActionMessage">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="confirmActionCancelBtn"></button>
                        <button type="button" class="btn btn-primary" id="confirmActionConfirmBtn"></button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }
    
    // 设置消息和按钮文本
    document.getElementById('confirmActionMessage').textContent = message;
    document.getElementById('confirmActionConfirmBtn').textContent = confirmText;
    document.getElementById('confirmActionCancelBtn').textContent = cancelText;
    
    // 创建Bootstrap模态框实例
    const modalInstance = new bootstrap.Modal(modal);
    
    // 绑定确认按钮事件
    const confirmBtn = document.getElementById('confirmActionConfirmBtn');
    const handleConfirm = function() {
        modalInstance.hide();
        callback();
        confirmBtn.removeEventListener('click', handleConfirm);
    };
    
    confirmBtn.addEventListener('click', handleConfirm);
    
    // 显示模态框
    modalInstance.show();
    
    // 模态框隐藏时清理事件监听
    modal.addEventListener('hidden.bs.modal', function() {
        confirmBtn.removeEventListener('click', handleConfirm);
    }, { once: true });
}

/**
 * 格式化日期时间
 * @param {string|Date} datetime - 日期时间字符串或Date对象
 * @param {string} format - 格式化模式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(datetime, format = 'YYYY-MM-DD HH:mm:ss') {
    const date = datetime instanceof Date ? datetime : new Date(datetime);
    
    if (isNaN(date.getTime())) {
        return 'Invalid Date';
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
} 