#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库连接问题修复脚本
"""

import os
import sys

def update_env_file():
    """更新.env文件，添加更好的数据库连接配置"""
    env_content = """FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=hard-to-guess-string-for-keyword-app

# 数据库连接配置
DB_USERNAME=keyword_db
DB_PASSWORD=SfTaBtjTSm8mLXwj
DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=keyword_db

# 完整的数据库连接URL - 添加连接参数
DEV_DATABASE_URL=mysql+pymysql://keyword_db:SfTaBtjTSm8mLXwj@127.0.0.1:3306/keyword_db?charset=utf8mb4&connect_timeout=60&read_timeout=30&write_timeout=30&autocommit=false
DATABASE_URL=mysql+pymysql://keyword_db:SfTaBtjTSm8mLXwj@127.0.0.1:3306/keyword_db?charset=utf8mb4&connect_timeout=60&read_timeout=30&write_timeout=30&autocommit=false
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ .env文件已更新")
        return True
    except Exception as e:
        print(f"❌ 更新.env文件失败: {e}")
        return False

def create_db_health_check():
    """创建数据库健康检查脚本"""
    health_check_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库健康检查脚本
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text
import os
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)

# 配置数据库连接
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DEV_DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 3600,
    'pool_timeout': 30,
    'max_overflow': 20,
    'connect_args': {
        'connect_timeout': 60,
        'read_timeout': 30,
        'write_timeout': 30,
        'charset': 'utf8mb4',
        'autocommit': False,
    }
}

db = SQLAlchemy(app)

def check_database():
    """检查数据库连接"""
    with app.app_context():
        try:
            # 测试连接
            result = db.session.execute(text('SELECT 1 as test'))
            test_value = result.scalar()
            print(f"✅ 数据库连接正常: {test_value}")
            
            # 检查表
            result = db.session.execute(text("SHOW TABLES LIKE 'keywords'"))
            table_exists = result.fetchone()
            if table_exists:
                result = db.session.execute(text("SELECT COUNT(*) FROM keywords"))
                count = result.scalar()
                print(f"✅ keywords表存在，记录数: {count}")
            else:
                print("⚠️  keywords表不存在")
            
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

if __name__ == '__main__':
    check_database()
'''
    
    try:
        with open('db_health_check.py', 'w', encoding='utf-8') as f:
            f.write(health_check_content)
        print("✅ 数据库健康检查脚本已创建")
        return True
    except Exception as e:
        print(f"❌ 创建健康检查脚本失败: {e}")
        return False

def print_recommendations():
    """打印修复建议"""
    print("\n" + "="*60)
    print("🔧 数据库连接问题修复建议:")
    print("="*60)
    
    print("\n1. 配置文件已更新，包含以下改进:")
    print("   - 启用连接预检查 (pool_pre_ping)")
    print("   - 增加连接回收时间到1小时")
    print("   - 添加连接超时配置")
    print("   - 优化连接池大小")
    
    print("\n2. 如果问题仍然存在，请检查:")
    print("   - MySQL服务是否正在运行")
    print("   - 数据库用户权限是否正确")
    print("   - 防火墙设置")
    print("   - MySQL的wait_timeout和interactive_timeout设置")
    
    print("\n3. 建议的MySQL配置 (my.cnf):")
    print("   wait_timeout = 28800")
    print("   interactive_timeout = 28800")
    print("   max_connections = 200")
    print("   max_allowed_packet = 64M")
    
    print("\n4. 重启应用:")
    print("   python app.py")
    
    print("\n5. 运行健康检查:")
    print("   python db_health_check.py")

def main():
    """主函数"""
    print("🔧 开始修复数据库连接问题...")
    
    success_count = 0
    total_tasks = 2
    
    if update_env_file():
        success_count += 1
    
    if create_db_health_check():
        success_count += 1
    
    print(f"\n修复任务完成: {success_count}/{total_tasks}")
    
    if success_count == total_tasks:
        print("✅ 所有修复任务完成")
        print_recommendations()
        return 0
    else:
        print("❌ 部分修复任务失败")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
