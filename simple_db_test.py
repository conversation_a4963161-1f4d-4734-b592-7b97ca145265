#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的数据库连接测试
"""

import pymysql
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_mysql_connection():
    """测试MySQL连接"""
    try:
        # 从环境变量获取数据库配置
        config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USERNAME', 'keyword_db'),
            'password': os.getenv('DB_PASSWORD', 'SfTaBtjTSm8mLXwj'),
            'database': os.getenv('DB_NAME', 'keyword_db'),
            'charset': 'utf8mb4',
            'connect_timeout': 10,
            'read_timeout': 10,
            'write_timeout': 10
        }
        
        print("尝试连接MySQL数据库...")
        print(f"主机: {config['host']}:{config['port']}")
        print(f"用户: {config['user']}")
        print(f"数据库: {config['database']}")
        
        # 创建连接
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            # 测试基本查询
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            print(f"✅ 连接成功，测试查询结果: {result}")
            
            # 检查MySQL版本
            cursor.execute("SELECT VERSION() as version")
            version = cursor.fetchone()
            print(f"MySQL版本: {version[0]}")
            
            # 检查重要变量
            cursor.execute("SHOW VARIABLES LIKE 'wait_timeout'")
            wait_timeout = cursor.fetchone()
            print(f"wait_timeout: {wait_timeout[1]} 秒")
            
            cursor.execute("SHOW VARIABLES LIKE 'interactive_timeout'")
            interactive_timeout = cursor.fetchone()
            print(f"interactive_timeout: {interactive_timeout[1]} 秒")
            
            # 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'keywords'")
            table_exists = cursor.fetchone()
            if table_exists:
                cursor.execute("SELECT COUNT(*) FROM keywords")
                count = cursor.fetchone()
                print(f"keywords表存在，记录数: {count[0]}")
            else:
                print("keywords表不存在")
        
        connection.close()
        print("✅ 数据库连接测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

if __name__ == '__main__':
    test_mysql_connection()
