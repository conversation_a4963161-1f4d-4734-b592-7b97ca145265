#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的应用
"""

import os
import sys
import time
import requests
from threading import Thread
from app import create_app

def start_app():
    """启动应用"""
    app = create_app('development')
    app.run(host='0.0.0.0', port=5301, debug=False, use_reloader=False)

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://127.0.0.1:5301"
    
    # 等待应用启动
    print("等待应用启动...")
    time.sleep(3)
    
    try:
        # 测试首页
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            print("✅ 首页访问正常")
        else:
            print(f"❌ 首页访问失败: {response.status_code}")
        
        # 测试API端点
        api_url = f"{base_url}/api/keywords?page=1&per_page=50&category=collection&status=true"
        response = requests.get(api_url, timeout=10)
        if response.status_code == 200:
            print("✅ API端点访问正常")
            data = response.json()
            print(f"返回数据: {len(data.get('data', []))} 条记录")
        else:
            print(f"❌ API端点访问失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🚀 启动修复后的应用测试...")
    
    # 在后台启动应用
    app_thread = Thread(target=start_app, daemon=True)
    app_thread.start()
    
    # 测试API
    test_api_endpoints()
    
    print("测试完成，按Ctrl+C退出")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n应用已停止")

if __name__ == '__main__':
    main()
