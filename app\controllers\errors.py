from flask import Blueprint, jsonify, render_template, request

errors_bp = Blueprint('errors', __name__)

@errors_bp.app_errorhandler(404)
def page_not_found(e):
    if request.accept_mimetypes.accept_json and \
            not request.accept_mimetypes.accept_html:
        response = jsonify({'error': '未找到资源', 'code': 404})
        response.status_code = 404
        return response
    return render_template('404.html'), 404

@errors_bp.app_errorhandler(500)
def internal_server_error(e):
    if request.accept_mimetypes.accept_json and \
            not request.accept_mimetypes.accept_html:
        response = jsonify({'error': '服务器内部错误', 'code': 500})
        response.status_code = 500
        return response
    return render_template('500.html'), 500 